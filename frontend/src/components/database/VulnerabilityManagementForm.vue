<template>
  <div class="vulnerability-management-form">
    <!-- 全局消息提示 -->
    <div v-if="globalError" class="error-message global-error">{{ globalError }}</div>
    <div v-if="globalSuccess" class="success-message global-success">{{ globalSuccess }}</div>

    <!-- 第一部分: 漏洞定义管理 -->
    <section class="vulnerability-definitions-section card">
      <h3 class="card-header">
        <i class="icon-list"></i> 漏洞定义管理
        <button @click="openVulnerabilityModal('create')" class="btn btn-sm btn-success float-right">
          <i class="icon-plus"></i> 添加新漏洞
        </button>
      </h3>
      <div class="card-body">
        <div v-if="definitionsLoading" class="loading-message">加载漏洞定义列表中...</div>
        <div v-else>
          <table v-if="vulnerabilityDefinitions && vulnerabilityDefinitions.length > 0" class="table table-striped table-hover">
            <thead>
              <tr>
                <th>CVE ID</th>
                <th>漏洞名称</th>
                <th>严重程度</th>
                <th>影响协议</th>
                <th>发布日期</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="vuln in vulnerabilityDefinitions" :key="vuln.vulnerability_id">
                <td>{{ vuln.cve_id }}</td>
                <td>{{ vuln.name }}</td>
                <td>
                  <span class="severity-badge" :class="'severity-' + vuln.severity.toLowerCase()">
                    {{ getSeverityText(vuln.severity) }}
                  </span>
                </td>
                <td>{{ vuln.affected_protocols || '-' }}</td>
                <td>{{ formatDate(vuln.published_date) || '-' }}</td>
                <td>
                  <button @click="openVulnerabilityModal('edit', vuln)" class="btn btn-sm btn-info mr-1">
                    <i class="icon-edit"></i> 编辑
                  </button>
                  <button @click="confirmDeleteVulnerability(vuln)" class="btn btn-sm btn-danger">
                    <i class="icon-delete"></i> 删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
          <p v-else-if="!storeError">暂无漏洞定义数据。请添加新漏洞。</p>
          <!-- Display storeError if it occurred during definitions fetch and definitions are empty -->
          <p v-if="storeError && (!vulnerabilityDefinitions || vulnerabilityDefinitions.length === 0)" class="error-message">
            加载漏洞定义失败: {{ storeError }}
          </p>
        </div>
      </div>
    </section>

    <!-- 漏洞定义编辑/创建模态框 -->
    <div v-if="showVulnerabilityModal" class="modal-overlay" @click.self="closeVulnerabilityModal">
      <div class="modal-content">
        <h4>{{ modalMode === 'create' ? '添加新漏洞' : '编辑漏洞' }}</h4>
        <form @submit.prevent="handleSaveVulnerabilityDefinition">
          <div class="form-group">
            <label for="vuln-cve">CVE ID <span class="required">*</span></label>
            <input type="text" id="vuln-cve" v-model="currentVulnerability.cve_id" required />
          </div>
          <div class="form-group">
            <label for="vuln-name">漏洞名称 <span class="required">*</span></label>
            <input type="text" id="vuln-name" v-model="currentVulnerability.name" required />
          </div>
          <div class="form-group">
            <label for="vuln-description">描述</label>
            <textarea id="vuln-description" v-model="currentVulnerability.description"></textarea>
          </div>
          <div class="form-group">
            <label for="vuln-severity">严重程度 <span class="required">*</span></label>
            <select id="vuln-severity" v-model="currentVulnerability.severity" required>
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="critical">严重</option>
            </select>
          </div>
          <div class="form-group">
            <label for="vuln-affected-protocols">影响协议 (逗号分隔)</label>
            <input type="text" id="vuln-affected-protocols" v-model="currentVulnerability.affected_protocols" />
          </div>
           <div class="form-group">
            <label for="vuln-detection-method">检测方法</label>
            <input type="text" id="vuln-detection-method" v-model="currentVulnerability.detection_method" />
          </div>
          <div class="form-group">
            <label for="vuln-published-date">发布日期</label>
            <input type="date" id="vuln-published-date" v-model="currentVulnerability.published_date" />
          </div>
          <div v-if="modalError" class="error-message">{{ modalError }}</div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary" :disabled="modalSubmitting">
              {{ modalSubmitting ? '保存中...' : '保存' }}
            </button>
            <button type="button" class="btn btn-secondary" @click="closeVulnerabilityModal" :disabled="modalSubmitting">取消</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 第二部分: 批量更新ASN漏洞受影响地址数 -->
    <section class="batch-update-section card mt-4">
      <h3 class="card-header">
        <i class="icon-update"></i> 批量更新ASN漏洞受影响地址数
      </h3>
      <div class="card-body">
        <div class="batch-operations-table">
          <div class="batch-operations-header">
            <div>漏洞 <span class="required">*</span></div>
            <div>国家 <span class="required">*</span></div>
            <div>ASN <span class="required">*</span></div>
            <div>操作 <span class="required">*</span></div>
            <div>数值 <span class="required">*</span></div>
            <div></div> <!-- 用于删除按钮列 -->
          </div>
          <div v-for="(op, index) in batchUpdateOperations" :key="op.id" class="batch-operation-row">
            <!-- 漏洞选择 -->
            <div class="form-group">
              <select v-model="op.vulnerabilityId" required>
                <option disabled value="">选择漏洞</option>
                <option v-for="vulnDef in vulnerabilityDefinitions" :key="vulnDef.vulnerability_id" :value="vulnDef.vulnerability_id">
                  {{ vulnDef.name }} ({{ vulnDef.cve_id }})
                </option>
              </select>
            </div>

            <!-- 国家选择 -->
            <div class="form-group search-container">
              <input
                type="text"
                v-model="op.countrySearch"
                placeholder="搜索或选择国家"
                @input="debouncedSearchCountries(index)"
                @focus="op.showCountryResults = true"
                @blur="handleBlurCountrySearch(index)"
              />
              <ul v-if="op.showCountryResults && op.matchedCountries.length" class="search-results">
                <li v-for="country in op.matchedCountries" :key="country.country_id" @mousedown.prevent="selectCountryForRow(index, country)">
                  {{ country.country_name_zh || country.country_name }} ({{country.country_id}})
                </li>
              </ul>
              <input type="hidden" v-model="op.countryId" /> <!-- required in form logic -->
            </div>

            <!-- ASN选择 -->
            <div class="form-group search-container">
              <input
                type="text"
                v-model="op.asnSearch"
                placeholder="搜索或选择ASN"
                @input="debouncedSearchAsns(index)"
                @focus="op.showAsnResults = true"
                @blur="handleBlurAsnSearch(index)"
              />
              <ul v-if="op.showAsnResults && op.matchedAsns.length" class="search-results">
                <li v-for="asnItem in op.matchedAsns" :key="asnItem.asn" @mousedown.prevent="selectAsnForRow(index, asnItem)">
                  {{ asnItem.as_name_zh || asnItem.as_name }} (AS{{ asnItem.asn }})
                </li>
              </ul>
               <input type="hidden" v-model="op.asn" /> <!-- required in form logic -->
            </div>
            
            <!-- 操作类型选择 -->
            <div class="form-group">
              <select v-model="op.updateAction" required>
                <option value="set">直接设置</option>
                <option value="increment">增加</option>
                <option value="decrement">减少</option>
              </select>
            </div>

            <!-- 数值输入 -->
            <div class="form-group">
              <input type="number" v-model.number="op.value" placeholder="数量" required min="0" />
            </div>

            <!-- 删除按钮 -->
            <div>
              <button @click="removeBatchOperation(index)" class="btn btn-sm btn-danger" :disabled="batchUpdateOperations.length <= 1">
                <i class="icon-minus"></i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="form-actions mt-3">
          <button @click="addBatchOperation" class="btn btn-secondary mr-2">
            <i class="icon-plus"></i> 添加更新项
          </button>
          <button @click="handleBatchUpdate" class="btn btn-primary" :disabled="isBatchSubmitting || !isBatchFormValid">
            <i class="icon-upload"></i> 
            {{ isBatchSubmitting ? '提交中...' : '提交批量更新' }}
          </button>
        </div>
        <p class="hint mt-2">提示: 如果更新的ASN和漏洞组合在统计表中不存在，系统将自动创建新记录。更新结果将在下方显示。</p>
        <div v-if="batchUpdateResults.length > 0" class="batch-results mt-3">
            <h4>批量更新结果:</h4>
            <ul>
                <li v-for="(result, idx) in batchUpdateResults" :key="idx" :class="result.success ? 'text-success' : 'text-danger'">
                    操作 {{ idx + 1 }}: {{ result.message }}
                    <span v-if="!result.success && result.originalOp"> (漏洞: {{ getVulnNameById(result.originalOp.vulnerabilityId) }}, ASN: {{result.originalOp.asn}})</span>
                </li>
            </ul>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { useDatabaseStore } from '@/stores/database';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash'; 
let operationIdCounter = 0;
const store = useDatabaseStore();
const { 
  vulnerabilityDefinitions, 
  error: storeError,
  isLoading: storeLoading 
} = storeToRefs(store);

// --- 全局消息 ---
const globalError = ref('');
const globalSuccess = ref('');

const getVulnNameById = (vulnerabilityId) => {
  if (!vulnerabilityId || !vulnerabilityDefinitions.value) return '未知漏洞';
  const vuln = vulnerabilityDefinitions.value.find(v => v.vulnerability_id === vulnerabilityId);
  return vuln ? `${vuln.name} (${vuln.cve_id})` : '未知漏洞';
};
// --- 漏洞定义管理 ---
const definitionsLoading = ref(false);
const showVulnerabilityModal = ref(false);
const modalMode = ref('create'); // 'create' or 'edit'
const currentVulnerability = ref({});
const modalSubmitting = ref(false);
const modalError = ref('');

const defaultVulnerability = {
  cve_id: '',
  name: '',
  description: '',
  severity: 'medium',
  affected_protocols: '',
  detection_method: '',
  published_date: null,
};

const loadVulnerabilityDefinitions = async () => {
  console.log('[VulnerabilityManagementForm] Attempting to load vulnerability definitions...');
  definitionsLoading.value = true;
  globalError.value = ''; 
  try {
    await store.fetchVulnerabilityDefinitions();
    // Log after fetch attempt
    await nextTick(); // Ensure Vue has updated reactive values if possible
    console.log('[VulnerabilityManagementForm] Vulnerability definitions loaded. Count:', vulnerabilityDefinitions.value?.length, 'Store error:', storeError.value);
    if (storeError.value && !vulnerabilityDefinitions.value?.length) { // If there's a store error and no definitions
      globalError.value = `加载漏洞定义失败: ${storeError.value}`;
    }
  } catch (error) {
    console.error('[VulnerabilityManagementForm] Error in loadVulnerabilityDefinitions catch block:', error);
    globalError.value = `加载漏洞定义时发生意外错误: ${error.message}`;
  } finally {
    definitionsLoading.value = false;
    console.log('[VulnerabilityManagementForm] definitionsLoading set to false.');
  }
};

const loadInitialDataForBatchOperations = async () => {
  console.log('[VulnerabilityManagementForm] Attempting to load countries for batch operations...');
  try {
    // 初始化allCountries为空数组，确保它始终是一个数组
    allCountries.value = [];
    
    // 尝试加载国家列表，使用大的limit获取所有国家
    await store.getCountries(1, 500, '');
    console.log('[VulnerabilityManagementForm] Countries loaded from store:', store.countries?.length || 0);
    
    // 直接使用store.countries而不是countriesList
    if (store.countries && store.countries.length > 0) {
      allCountries.value = [...store.countries];
      console.log('[VulnerabilityManagementForm] Countries copied to allCountries:', allCountries.value.length);
    } else {
      console.error('[VulnerabilityManagementForm] countries is empty after getCountries call');
      globalError.value = '无法加载国家列表，请刷新页面重试';
    }
    
    // 初始化第一个批量操作行的匹配国家
    if (batchUpdateOperations.value.length > 0) {
      batchUpdateOperations.value.forEach(op => {
        op.matchedCountries = allCountries.value.slice(0, 20) || [];
        console.log(`[VulnerabilityManagementForm] Initialized matchedCountries for operation:`, op.matchedCountries.length);
      });
    }
  } catch (error) {
    console.error('[VulnerabilityManagementForm] Error in loadInitialDataForBatchOperations:', error);
    globalError.value = `加载国家列表时发生意外错误: ${error.message}`;
  }
};

function openVulnerabilityModal(mode, vuln = null) {
  modalMode.value = mode;
  modalError.value = '';
  if (mode === 'create') {
    currentVulnerability.value = { ...defaultVulnerability };
  } else {
    currentVulnerability.value = { 
      ...vuln, 
      published_date: vuln.published_date ? vuln.published_date.split('T')[0] : null 
    };
  }
  showVulnerabilityModal.value = true;
}

function closeVulnerabilityModal() {
  showVulnerabilityModal.value = false;
  currentVulnerability.value = {};
}

async function handleSaveVulnerabilityDefinition() {
  modalSubmitting.value = true;
  modalError.value = '';
  globalSuccess.value = '';
  try {
    const dataToSave = { ...currentVulnerability.value };
    if (!dataToSave.published_date) { // Handle empty date string
        delete dataToSave.published_date;
    }

    if (modalMode.value === 'create') {
      await store.createVulnerabilityDefinition(dataToSave);
      globalSuccess.value = '漏洞定义创建成功!';
    } else {
      await store.updateVulnerabilityDefinition(currentVulnerability.value.vulnerability_id, dataToSave);
      globalSuccess.value = '漏洞定义更新成功!';
    }
    closeVulnerabilityModal();
    setTimeout(() => globalSuccess.value = '', 3000);
  } catch (error) {
    modalError.value = error.response?.data?.message || error.message || '保存失败';
  } finally {
    modalSubmitting.value = false;
  }
}

async function confirmDeleteVulnerability(vuln) {
  if (window.confirm(`确定要删除漏洞 "${vuln.name}" (CVE: ${vuln.cve_id})吗?`)) {
    definitionsLoading.value = true; // Use general loading or a specific one
    globalError.value = '';
    globalSuccess.value = '';
    try {
      await store.deleteVulnerabilityDefinition(vuln.vulnerability_id);
      globalSuccess.value = '漏洞定义删除成功!';
      setTimeout(() => globalSuccess.value = '', 3000);
    } catch (error) {
      globalError.value = error.response?.data?.message || error.message || '删除失败';
      setTimeout(() => globalError.value = '', 5000);
    } finally {
      definitionsLoading.value = false;
    }
  }
}

function getSeverityText(severity) {
  const map = { low: '低', medium: '中', high: '高', critical: '严重' };
  return map[severity.toLowerCase()] || severity;
}

function formatDate(dateString) {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (e) {
    return dateString; // fallback
  }
}

function removeBatchOperation(index) {
  if (batchUpdateOperations.value.length > 1) {
    batchUpdateOperations.value.splice(index, 1);
  }
}

// --- 批量更新ASN漏洞受影响地址数 ---
const batchUpdateOperations = ref([createBatchOperation()]);
const isBatchSubmitting = ref(false);
const batchUpdateResults = ref([]); // To store results of batch submission

function createBatchOperation() {
  operationIdCounter++;
  return {
    id: operationIdCounter,
    vulnerabilityId: '',
    countryId: '',
    countrySearch: '',
    matchedCountries: [],
    showCountryResults: false,
    asn: '',
    asnSearch: '',
    matchedAsns: [],
    showAsnResults: false,
    updateAction: 'set',
    value: 0,
  };
}

const isBatchFormValid = computed(() => {
  return batchUpdateOperations.value.every(op => 
    op.vulnerabilityId && op.countryId && op.asn && op.updateAction && op.value >= 0
  );
});

// 添加 resetForm 函数定义
function resetForm() {
  // 重置批量更新操作列表，保留一个空行
  batchUpdateOperations.value = [createBatchOperation()];
  // 清空批量更新结果
  batchUpdateResults.value = [];
  // 清空全局消息
  globalError.value = '';
  globalSuccess.value = '';
}

async function handleBatchUpdate() {
  try {
    console.log('[Form] 开始批量更新，操作数量:', batchUpdateOperations.value.length);
    
    // 验证所有操作
    const validOperations = batchUpdateOperations.value.filter(op => {
      if (!op.vulnerabilityId || !op.asn || !op.updateAction || op.value === undefined) {
        console.warn('[Form] 跳过无效操作:', op);
        return false;
      }
      return true;
    });

    if (validOperations.length === 0) {
      globalError.value = '没有有效的更新操作';
      return;
    }

    // 准备批量更新数据
    const operations = validOperations.map(op => ({
      vulnerabilityId: op.vulnerabilityId,
      asn: op.asn,
      updateAction: op.updateAction,
      value: Number(op.value)
    }));

    console.log('[Form] 提交批量更新操作:', operations);
    
    // 调用store的批量更新方法
    const result = await store.batchUpdateAsnVulnerabilityStats(operations);
    console.log('[Form] 批量更新结果:', result);

    // 处理更新结果
    if (result.success) {
      // 完全成功
      globalSuccess.value = result.message || '批量更新成功';
      // 将结果添加到批量更新结果列表
      batchUpdateResults.value = result.results.map(op => ({
        success: true,
        message: `操作成功: ${getVulnNameById(op.vulnerabilityId)} (ASN: ${op.asn}) - ${op.message || '更新成功'}`,
        originalOp: op
      }));
      // 重置表单
      resetForm();
    } else {
      // 部分成功或完全失败
      const failedOperations = result.results.filter(r => !r.success);
      if (failedOperations.length > 0) {
        // 构建错误消息
        const errorMessages = failedOperations.map(op => {
          const vulnerability = vulnerabilityDefinitions.value.find(v => v.vulnerability_id === op.vulnerabilityId);
          const asn = allAsns.value.find(a => a.asn === op.asn);
          return {
            success: false,
            message: `操作失败: ${vulnerability?.name || '未知漏洞'} (ASN: ${asn?.asn || op.asn}) - ${op.message || '未知错误'}`,
            originalOp: op
          };
        });
        // 将成功和失败的操作都添加到结果列表
        batchUpdateResults.value = [
          ...result.results.filter(r => r.success).map(op => ({
            success: true,
            message: `操作成功: ${getVulnNameById(op.vulnerabilityId)} (ASN: ${op.asn}) - ${op.message || '更新成功'}`,
            originalOp: op
          })),
          ...errorMessages
        ];
        globalError.value = '部分操作失败，请查看详细结果';
      } else {
        globalError.value = result.message || '批量更新失败';
        batchUpdateResults.value = result.results.map(op => ({
          success: false,
          message: `操作失败: ${getVulnNameById(op.vulnerabilityId)} (ASN: ${op.asn}) - ${result.message || '更新失败'}`,
          originalOp: op
        }));
      }
    }
  } catch (error) {
    console.error('[Form] 批量更新失败:', error);
    globalError.value = error.message || '批量更新操作失败';
    batchUpdateResults.value = [{
      success: false,
      message: `操作失败: ${error.message || '未知错误'}`,
      originalOp: null
    }];
  }
}

// --- 国家和ASN搜索逻辑 ---
const allCountries = ref([]); // Store all countries fetched once
const allAsns = ref([]); // 存储所有ASN

async function loadInitialDataForSelectors() {
  try {
    // 加载国家列表，使用较大的limit以获取所有国家
    await store.getCountries(1, 500, '');
    // 直接使用store的countries而不是countriesList
    allCountries.value = store.countries || [];
    console.log('[VulnerabilityManagementForm] 加载国家列表完成，数量:', allCountries.value.length);
    
    // 初始化第一个批量操作行的匹配国家 - 显示所有国家
    if (batchUpdateOperations.value.length > 0) {
      batchUpdateOperations.value.forEach(op => {
        op.matchedCountries = allCountries.value.slice(0, 20) || [];
      });
    }
    
    // 预加载ASN数据
    try {
      console.log('[Form] 开始预加载ASN数据...');
      const asnsData = await store.getAllAsns(1, 20); // 获取前20个ASN
      console.log('[Form] 预加载ASN数据成功，响应:', asnsData);
      
      // 确保asnsData是数组且不为空
      if (Array.isArray(asnsData) && asnsData.length > 0) {
        console.log('[Form] 预加载ASN数据成功，数量:', asnsData.length);
        allAsns.value = asnsData;
        
        // 初始化每个操作行的ASN列表
        if (batchUpdateOperations.value.length > 0) {
          batchUpdateOperations.value.forEach(op => {
            op.matchedAsns = [...asnsData]; // 复制预加载的ASN到每个操作行
            console.log(`[Form] 初始化: 为操作行设置了${op.matchedAsns.length}个ASN`);
          });
        } else {
          console.warn('[Form] 无法初始化ASN列表: 操作行为空');
        }
      } else {
        console.warn('[Form] 预加载ASN数据为空或格式不正确:', asnsData);
        allAsns.value = [];
        // 初始化空数组
        if (batchUpdateOperations.value.length > 0) {
          batchUpdateOperations.value.forEach(op => {
            op.matchedAsns = [];
          });
        }
      }
    } catch (error) {
      console.error('[Form] 预加载ASN数据失败:', error);
      globalError.value = '加载ASN列表失败，请刷新页面重试';
      allAsns.value = [];
      // 初始化空数组
      if (batchUpdateOperations.value.length > 0) {
        batchUpdateOperations.value.forEach(op => {
          op.matchedAsns = [];
        });
      }
    }
  } catch (error) {
    console.error('[Form] 加载选择器初始数据失败:', error);
    globalError.value = '加载初始数据失败，请刷新页面重试';
  }
}

function _debounce(fn, delay) {
  let timeoutID = null;
  return function(...args) {
    clearTimeout(timeoutID);
    timeoutID = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

const debouncedSearchCountries = _debounce((rowIndex) => {
  const op = batchUpdateOperations.value[rowIndex];
  if (!op) return;
  
  console.log(`[Country Search] Searching for: "${op.countrySearch}" in ${allCountries.value.length} countries`);
  
  const searchTerm = op.countrySearch.toLowerCase();
  if (!searchTerm) {
    // 移除20个限制，显示所有国家
    op.matchedCountries = [...allCountries.value];
    return;
  }
  
  op.matchedCountries = allCountries.value.filter(country => {
    const nameCN = (country.country_name_zh || '').toLowerCase();
    const nameEN = (country.country_name || '').toLowerCase();
    const id = (country.country_id || '').toLowerCase();
    return nameCN.includes(searchTerm) || nameEN.includes(searchTerm) || id.includes(searchTerm);
  });
}, 300);

//搜索ASN
const searchAsns = async (index) => {
  const op = batchUpdateOperations.value[index];
  console.log(`[Form] 搜索ASN: 行=${index}, 查询='${op.asnSearch}', 国家ID=${op.countryId || 'none'}`);
  
  try {
    let asns = [];
    
    if (op.asnSearch && op.asnSearch.length >= 2) {
      // 如果有搜索词，根据搜索词查询
      console.log(`[Form] 通过搜索词查询ASN: ${op.asnSearch}`);
      asns = await store.searchAsns(op.asnSearch, op.countryId);
    } else if (op.countryId) {
      // 如果有国家ID但没有搜索词，获取该国家的ASN
      console.log(`[Form] 获取国家的ASN: ${op.countryId}`);
      asns = await store.fetchAsnsByCountry(op.countryId);
    } else {
      // 如果既没有国家ID也没有搜索词，获取所有ASN
      console.log(`[Form] 获取所有ASN (无国家ID和搜索词)`);
      const response = await store.getAllAsns(1, 1000); // 增加获取数量
      asns = response.data || [];
    }
    
    console.log(`[Form] 获取到ASN数量: ${asns.length}`);
    op.matchedAsns = asns.slice(0, 20);
    
    if (op.matchedAsns.length === 0 && op.asnSearch) {
      console.warn(`[Form] 未找到匹配的ASN: ${op.asnSearch}`);
    }
  } catch (error) {
    console.error(`[Form] 搜索ASN失败:`, error);
    op.matchedAsns = [];
  }
};

// 初始化批量操作行时加载ASN
const initializeRowAsns = async (index) => {
  const op = batchUpdateOperations.value[index];
  console.log(`[Form] 初始化行${index}的ASN列表`);
  
  try {
    // 默认加载一些ASN，无论是否有国家ID
    let asns = [];
    
    if (op.countryId) {
      console.log(`[Form] 初始化: 加载国家${op.countryId}的ASN`);
      asns = await store.fetchAsnsByCountry(op.countryId);
    } else {
      console.log(`[Form] 初始化: 加载所有ASN (无国家ID)`);
      asns = await store.getAllAsns(1, 20);
    }
    
    console.log(`[Form] 初始化: 获取到${asns.length}个ASN`);
    op.matchedAsns = asns.slice(0, 20);
  } catch (error) {
    console.error(`[Form] 初始化ASN列表失败:`, error);
    op.matchedAsns = [];
  }
};
  
// 当添加新的批量操作行时，初始化ASN
const addBatchOperation = () => {
  const newOp = createBatchOperation();
  batchUpdateOperations.value.push(newOp);
  
  // 初始化新行的国家和ASN列表
  nextTick(() => {
    const index = batchUpdateOperations.value.length - 1;
    if (allCountries.value.length > 0) {
      batchUpdateOperations.value[index].matchedCountries = allCountries.value.slice(0, 20);
    }
    
    // 初始化ASN列表，即使没有选择国家
    initializeRowAsns(index);
  });
};

// 当选择国家时，更新ASN列表
const selectCountryForRow = (index, country) => {
  const op = batchUpdateOperations.value[index];
  op.countryId = country.country_id;
  op.countrySearch = country.country_name_zh || country.country_name;
  op.showCountryResults = false;
  
  // 当选择国家后，更新ASN列表
  console.log(`[Form] 选择国家后更新ASN列表: 国家=${country.country_id}`);
  nextTick(() => {
    searchAsns(index);
  });
};



// 当国家搜索框失去焦点时
const handleBlurCountrySearch = (index) => {
  // 延迟关闭下拉菜单，以便用户可以点击选项
  setTimeout(() => {
    batchUpdateOperations.value[index].showCountryResults = false;
  }, 200);
};

// 当ASN搜索框失去焦点时
const handleBlurAsnSearch = (index) => {
  // 延迟关闭下拉菜单，以便用户可以点击选项
  setTimeout(() => {
    batchUpdateOperations.value[index].showAsnResults = false;
  }, 200);
};


const debouncedSearchAsns = debounce(async (index) => {
  const op = batchUpdateOperations.value[index];
  const query = op.asnSearch.trim();
  
  try {
    console.log(`[ASN Search] Searching ASNs with query: '${query}', country: ${op.countryId || 'none'}`);
    
    let asnsData = [];
    if (query && query.length >= 2) {
      // 如果有搜索词，使用搜索API
      asnsData = await store.searchAsns(query, op.countryId);
      console.log(`[ASN Search] Searched ASNs with query '${query}', found ${asnsData.length}`);
    } else if (op.countryId) {
      // 如果有国家ID但没有搜索词，获取该国家的ASN
      asnsData = await store.fetchAsnsByCountry(op.countryId);
      console.log(`[ASN Search] Fetched ${asnsData.length} ASNs for country ${op.countryId}`);
    } else {
      // 如果既没有国家ID也没有搜索词，获取所有ASN
      const response = await store.getAllAsns(1, 1000);
      asnsData = response.data || [];
      console.log(`[ASN Search] Fetched all ASNs, count: ${asnsData.length}`);
    }
    
    op.matchedAsns = asnsData;
  } catch (error) {
    console.error('[ASN Search] Error:', error);
    op.matchedAsns = [];
  }
}, 300);

// 根据国家ID搜索ASN
async function searchAsnsByCountry(rowIndex) {
  const op = batchUpdateOperations.value[rowIndex];
  if (!op || !op.countryId) return;
  
  try {
    const results = await store.fetchAsnsByCountry(op.countryId);
    op.matchedAsns = results;
    console.log(`[ASN Search] Found ${results.length} ASNs for country ${op.countryId}`);
  } catch (error) {
    console.error(`[ASN Search] Error fetching ASNs for country ${op.countryId}:`, error);
    op.matchedAsns = [];
  }
}



function selectAsnForRow(index, asnItem) {
  const op = batchUpdateOperations.value[index];
  op.asn = asnItem.asn;
  op.asnSearch = `${asnItem.as_name_zh || asnItem.as_name} (AS${asnItem.asn})`;
  op.showAsnResults = false;
  
  // 如果ASN有国家信息，强制设置国家
  if (asnItem.country_id) {
    const country = allCountries.value.find(c => c.country_id === asnItem.country_id);
    if (country) {
      op.countryId = country.country_id;
      op.countrySearch = country.country_name_zh || country.country_name;
      console.log(`[ASN Selection] Set country to ${op.countryId} (${op.countrySearch}) based on selected ASN`);
    } else {
      // 如果在本地找不到国家信息，尝试从服务器获取
      store.getCountries().then(() => {
        const updatedCountry = allCountries.value.find(c => c.country_id === asnItem.country_id);
        if (updatedCountry) {
          op.countryId = updatedCountry.country_id;
          op.countrySearch = updatedCountry.country_name_zh || updatedCountry.country_name;
          console.log(`[ASN Selection] Set country to ${op.countryId} (${op.countrySearch}) after fetching countries`);
        }
      });
    }
  }
}


onMounted(async () => {
  console.log('[VulnerabilityManagementForm] Component mounted, loading initial data...');
  await loadVulnerabilityDefinitions();
  await loadInitialDataForBatchOperations(); // 确保国家列表已加载
  await loadInitialDataForSelectors(); 
  
  // 添加调试日志，检查ASN数据是否正确加载
  console.log('[Form] 初始化: 获取到', allAsns.value?.length || 0, '个ASN');
  console.log('[Form] 初始化: 第一个操作行的matchedAsns长度:', 
    batchUpdateOperations.value[0]?.matchedAsns?.length || 0);
  
  // 确保allCountries已初始化
  if (!allCountries.value || allCountries.value.length === 0) {
    console.warn('[VulnerabilityManagementForm] allCountries is empty after initialization, trying again...');
    // 如果store.countries有值但allCountries没有，尝试再次复制
    if (store.countries && store.countries.length > 0) {
      allCountries.value = [...store.countries];
      console.log('[VulnerabilityManagementForm] Copied store.countries to allCountries:', allCountries.value.length);
    }
  }
  
  // 确保allAsns已初始化
  if (!allAsns.value || allAsns.value.length === 0) {
    console.warn('[Form] allAsns为空，尝试重新加载...');
    try {
      const response = await store.getAllAsns(1, 20);
      const asnsData = response.data?.data || [];
      allAsns.value = asnsData;
      console.log('[Form] 重新加载ASN成功，数量:', allAsns.value.length);
      
      // 更新操作行的ASN列表
      if (batchUpdateOperations.value.length > 0) {
        batchUpdateOperations.value[0].matchedAsns = [...allAsns.value];
      }
    } catch (error) {
      console.error('[Form] 重新加载ASN失败:', error);
    }
  }

  console.log('[VulnerabilityManagementForm] Initial data loading complete.');
});

// Watch for store errors to update globalError
watch(storeError, (newError) => {
  if (newError && !definitionsLoading.value && !modalSubmitting.value && !isBatchSubmitting.value) {
    // Only show if not related to an active operation that has its own error display
    globalError.value = newError;
  }
});

</script>

<style scoped>
/* 全局样式 */
.vulnerability-management-form {
  font-family: 'Arial', sans-serif;
  color: #333;
}

.card {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.card-header {
  background-color: #f8f9fa;
  padding: 12px 20px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 1.1em;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header .icon-list,
.card-header .icon-update {
  margin-right: 8px;
}

.card-body {
  padding: 20px;
}

/* 消息提示 */
.error-message, .success-message {
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  font-size: 0.9em;
}
.global-error, .global-success {
  margin: 0 0 20px 0; /* Adjust margin for global messages if they are outside cards */
}
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.loading-message {
  color: #555;
  padding: 20px;
  text-align: center;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}
.table th, .table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
  text-align: left;
}
.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
  background-color: #f8f9fa;
}
.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0,0,0,.03);
}
.table-hover tbody tr:hover {
  background-color: rgba(0,0,0,.06);
}

/* 按钮样式 */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.9rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  cursor: pointer;
}
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.7875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.btn-primary { color: #fff; background-color: #007bff; border-color: #007bff; }
.btn-primary:hover { background-color: #0056b3; border-color: #0056b3; }
.btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
.btn-secondary:hover { background-color: #545b62; border-color: #545b62; }
.btn-success { color: #fff; background-color: #28a745; border-color: #28a745; }
.btn-success:hover { background-color: #1e7e34; border-color: #1e7e34; }
.btn-info { color: #fff; background-color: #17a2b8; border-color: #17a2b8; }
.btn-info:hover { background-color: #117a8b; border-color: #117a8b; }
.btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }
.btn-danger:hover { background-color: #bd2130; border-color: #bd2130; }
.btn:disabled { opacity: 0.65; cursor: not-allowed; }
.float-right { float: right; }
.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }

/* 表单元素 */
.form-group {
  margin-bottom: 1rem;
}
.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}
.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  box-sizing: border-box; /* Important for width 100% */
}
.form-group textarea {
  min-height: 80px;
  resize: vertical;
}
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
.required {
  color: #dc3545;
  margin-left: 2px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content {
  background-color: #fff;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
.modal-content h4 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25em;
}
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px; /* Spacing between buttons */
  margin-top: 1.5rem;
}

/* 严重程度徽章 */
.severity-badge {
  padding: 0.25em 0.6em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  color: #fff;
}
.severity-low { background-color: #28a745; } /* Green */
.severity-medium { background-color: #ffc107; color: #212529;} /* Yellow */
.severity-high { background-color: #fd7e14; } /* Orange */
.severity-critical { background-color: #dc3545; } /* Red */


/* 批量更新表格化布局 */
.batch-operations-table .form-group {
  width: 100%;
  margin-bottom: 0;
}

.batch-operations-table .form-group input,
.batch-operations-table .form-group select {
  width: 100%;
  height: 38px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.batch-operations-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr 40px;
  gap: 10px;
  padding: 10px 0;
  font-weight: bold;
  border-bottom: 2px solid #eee;
}

.batch-operation-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr 40px;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  align-items: center;
}

/* 搜索结果下拉框样式 */
.search-container {
  position: relative;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 0 0 4px 4px;
  z-index: 1000;
  padding: 0;
  margin: 0;
  list-style: none;
}

.search-results li {
  padding: 8px 12px;
  cursor: pointer;
}

.search-results li:hover {
  background-color: #f8f9fa;
}

/* 批量更新结果 */
.batch-results {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}
.batch-results h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1em;
  color: #495057;
}
.batch-results ul {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}
.batch-results li {
  padding: 8px 0;
  font-size: 0.95em;
  border-bottom: 1px solid #e9ecef;
}
.batch-results li:last-child {
  border-bottom: none;
}
.batch-results .text-success {
  color: #28a745;
}
.batch-results .text-danger {
  color: #dc3545;
}

.hint {
  font-size: 0.85em;
  color: #666;
  margin-top: 10px;
}

/* 图标 (假设你使用了类似FontAwesome的图标类) */
[class^="icon-"], [class*=" icon-"] {
  /* 基本的图标样式，具体需要根据你的图标库调整 */
  display: inline-block;
  font-family: 'your-icon-font'; /* 替换为你的图标字体名 */
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 示例图标 - 你需要用你的图标库的实际unicode或类名替换 */
.icon-list:before { content: "📋"; }
.icon-plus:before { content: "➕"; }
.icon-edit:before { content: "✏️"; }
.icon-delete:before { content: "🗑️"; }
.icon-update:before { content: "🔄"; }
.icon-minus:before { content: "➖"; }
.icon-upload:before { content: "📤"; }

</style>