// 全局变量
$primary-color: #42b983;
$secondary-color: #35495e;
$danger-color: #ff5252;
$warning-color: #ff9800;
$success-color: #4caf50;
$info-color: #2196f3;

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  line-height: 1.6;
}

// 全局工具类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 20px;
}

// 按W钮基础样式
.btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  &-primary {
    background-color: $primary-color;
    color: white;

    &:hover {
      background-color: darken($primary-color, 10%);
    }
  }

  &-danger {
    background-color: $danger-color;
    color: white;

    &:hover {
      background-color: darken($danger-color, 10%);
    }
  }
}

.form-group {
  select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    background-color: white;
    
    &:focus {
      outline: none;
      border-color: #42b983;
    }
  }
}