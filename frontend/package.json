{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@tweenjs/tween.js": "^25.0.0", "axios": "^1.8.4", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.8", "file-saver": "^2.0.5", "lodash": "^4.17.21", "marked": "^15.0.7", "mitt": "^3.0.1", "pinia": "^3.0.1", "three": "^0.175.0", "vue": "^3.5.13", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/d3": "^7.4.3", "@types/node": "^22.13.14", "@types/three": "^0.175.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.21.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "prettier": "3.5.3", "sass": "^1.86.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}