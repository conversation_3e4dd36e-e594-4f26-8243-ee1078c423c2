# IDE and Editor files
.cursor/
.trae/
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Dependencies
node_modules/
*/node_modules/

# Build outputs
dist/
dist-ssr/
build/
*/dist/
*/build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
backend_logs/
xmap_log/
zgrab2_logs/

# Results and data
xmap_result/
zgrab2_results/
IPv6_addresses/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# TypeScript cache
*.tsbuildinfo

# Temporary folders
tmp/
temp/

# Documentation (if you want to ignore it)
docs/

# Upload directories
backend/uploads/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Local configuration files
config.local.js
config.local.json

# Package lock files (optional - you might want to keep these)
# package-lock.json
# */package-lock.json 


xmap_result/
xmap_log/
IPv6_addresses/
zgrab2_logs/
zgrab2_config/
zgrab2_input/
zgrab2_results/
01-address/
jsonanalysis/
*.env