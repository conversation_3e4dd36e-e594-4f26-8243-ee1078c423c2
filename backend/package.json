{"name": "backend", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/three": "^0.175.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "pinia": "^3.0.1", "three": "^0.175.0", "uuid": "^11.1.0", "winston": "^3.17.0"}}